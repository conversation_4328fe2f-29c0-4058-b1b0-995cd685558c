import { test } from '../../test-objects/auth.fixture';
import { CampaignsPage } from '../../pages/campaigns.page';
import * as allure from "allure-js-commons";
import { Severity } from "allure-js-commons";
import { navigateWithBotAuth, navigateWithHelperBotAuth } from '../../test-objects/login_process';

test.describe('Campaigns Test Suite', () => {
    let campaignsPage: CampaignsPage;

    test('Add a new lead in a campaign', async ({ botAuth, logger }) => {
        try {
            campaignsPage = new CampaignsPage(botAuth.page);
            await allure.description("New Lead Test");
            await allure.severity(Severity.NORMAL);

            logger.info('Starting new lead test');

            await allure.step("Login to the InConnect platform", async () => {
                await navigateWithBotAuth(botAuth.page);
                logger.debug('Successfully logged in');
            });

            await allure.step("Navigate to Campaigns", async () => {
                await campaignsPage.navigateToCampaigns();
                logger.debug('Navigated to Campaigns page');
            });

            await allure.step("Navigate to a random campaign", async () => {
                await campaignsPage.selectRandomCampaign();
                logger.debug('Selected random campaign');
            });

            await allure.step("Confirm New Lead Modal", async () => {
                await campaignsPage.clickNewLeadButton();
                const isVisible = await campaignsPage.isNewLeadModalVisible();
                
                if (isVisible) {
                    logger.info('Lead modal is displayed with correct ARIA attributes');
                } else {
                    logger.error('Lead modal is not displayed or missing required ARIA attributes');
                    throw new Error('Lead modal verification failed');
                }
            });
        } catch (error) {
            const errorObj = error instanceof Error ? error : new Error(String(error));
            logger.error('Test failed', errorObj);
            throw errorObj;
        }
    });

    test('Create a new Campaign', async ({ helperBotAuth, logger }) => {
        try {
            campaignsPage = new CampaignsPage(helperBotAuth.page);
            await allure.description("New Campaign Test");
            await allure.severity(Severity.NORMAL);

            logger.info('Starting new campaign test');

            await allure.step("Login to the InConnect platform", async () => {
                await navigateWithHelperBotAuth(helperBotAuth.page);
                logger.debug('Successfully logged in');
            });

            await allure.step("Navigate to Campaigns", async () => {
                await campaignsPage.navigateToCampaigns();
                logger.debug('Navigated to Campaigns page');
            });

            await allure.step("Click on 'New Campaign' button", async () => {
                await campaignsPage.clickNewCampaignButton();
                logger.debug('Clicked New Campaign button');
            });

            await allure.step("Confirm New Campaign Modal", async () => {
                const isVisible = await campaignsPage.isNewCampaignModalVisible();
                
                if (isVisible) {
                    logger.info('Campaign modal is displayed with correct ARIA attributes');
                } else {
                    logger.error('Campaign modal is not displayed or missing required ARIA attributes');
                    throw new Error('Campaign modal verification failed');
                }
            });
        } catch (error) {
            const errorObj = error instanceof Error ? error : new Error(String(error));
            logger.error('Test failed', errorObj);
            throw errorObj;
        }
    });

    test('New Session', async ({ botAuth, logger }) => {
        try {
            campaignsPage = new CampaignsPage(botAuth.page);
            await allure.description("New Session Test");
            await allure.severity(Severity.NORMAL);

            logger.info('Starting new session test');

            await allure.step("Login to the InConnect platform", async () => {
                await navigateWithBotAuth(botAuth.page);
                logger.debug('Successfully logged in');
            });

            await allure.step("Navigate to Campaigns", async () => {
                await campaignsPage.navigateToCampaigns();
                logger.debug('Navigated to Campaigns page');
            });

            await allure.step("Navigate to a random campaign", async () => {
                await campaignsPage.selectRandomCampaign();
                logger.debug('Selected random campaign');
            });

            await allure.step("Click on 'Capture Sessions' button & 'New Session' button", async () => {
                await campaignsPage.clickCaptureSessions();
                logger.debug('Clicked Capture Sessions button');
                await campaignsPage.clickNewSession();
                logger.debug('Clicked New Session button');
                
                const isVisible = await campaignsPage.isNewSessionModalVisible();
                if (isVisible) {
                    logger.info('Session modal is displayed with correct ARIA attributes');
                } else {
                    logger.error('Session modal is not displayed or missing required ARIA attributes');
                    throw new Error('Session modal verification failed');
                }
            });
        } catch (error) {
            const errorObj = error instanceof Error ? error : new Error(String(error));
            logger.error('Test failed', errorObj);
            throw errorObj;
        }
    });

    test('Lead Upload', async ({ helperBotAuth }) => {
        campaignsPage = new CampaignsPage(helperBotAuth.page);
        await allure.description("Lead Upload Test");
        await allure.severity(Severity.NORMAL);

        await allure.step("Login to the InConnect platform", async () => {
            await navigateWithHelperBotAuth(helperBotAuth.page);
        });

        await allure.step("Navigate to Campaigns", async () => {
            await campaignsPage.navigateToCampaigns();
        });

        await allure.step("Navigate to a random campaign", async () => {
            await campaignsPage.selectRandomCampaign();
        });

        await allure.step("Lead Import process", async () => {
            await campaignsPage.clickImportLeadsButton();
            await campaignsPage.selectLeadGenerator();
            await campaignsPage.selectLeadManager();
            const isVisible = await campaignsPage.isLeadUploadModalVisible();
            console.log(isVisible ? "Lead upload modal is displayed" : "Lead upload modal is not displayed");
        });
    });

    test('Verify Campaign Tiles Display', async ({ botAuth, logger }) => {
        try {
            campaignsPage = new CampaignsPage(botAuth.page);
            await allure.description("Campaign Tiles Visibility Test");
            await allure.severity(Severity.NORMAL);

            logger.info('Starting campaign tiles visibility test');

            await allure.step("Login to the InConnect platform", async () => {
                await navigateWithBotAuth(botAuth.page);
                logger.debug('Successfully logged in');
            });

            await allure.step("Navigate to Campaigns", async () => {
                await campaignsPage.navigateToCampaigns();
                logger.debug('Navigated to Campaigns page');
            });

            await allure.step("Verify Campaign Tiles", async () => {
                const tilesVisible = await campaignsPage.areCampaignTilesVisible();
                if (tilesVisible) {
                    logger.info('Campaign tiles are displayed with correct ARIA attributes');
                } else {
                    logger.error('Campaign tiles are not displayed or missing required ARIA attributes');
                    throw new Error('Campaign tiles verification failed');
                }
            });
        } catch (error) {
            const errorObj = error instanceof Error ? error : new Error(String(error));
            logger.error('Test failed', errorObj);
            throw errorObj;
        }
    });

    test.afterEach(async ({ page }, testInfo) => {
        if (testInfo.status !== testInfo.expectedStatus) {
            // Take screenshot only on failure
            const screenshot = await page.screenshot();
            await testInfo.attach('screenshot', { body: screenshot, contentType: 'image/png' });
        }
    });
});