{"timestamp": "2025-07-14T21:02:59.082Z", "url": "https://crm.inscape.co.za/App/CrmUser/Campaign_Detail/71/", "selector": "body", "snapshot": {"role": "WebArea", "name": "Connect - CRM - Campaigns", "children": [{"role": "link", "name": " Admin"}, {"role": "link", "name": " Calendar"}, {"role": "text", "name": ""}, {"role": "link", "name": "Individual"}, {"role": "text", "name": ""}, {"role": "text", "name": "CRM"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Communication"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Enrolments"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Finances"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Academics"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "link", "name": "Facilities"}, {"role": "text", "name": ""}, {"role": "text", "name": "Classroom Management"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "link", "name": "Data Analytics"}, {"role": "text", "name": ""}, {"role": "link", "name": "Graduation"}, {"role": "text", "name": ""}, {"role": "link", "name": "Compliance"}, {"role": "text", "name": ""}, {"role": "link", "name": "Policies And Procedures"}, {"role": "text", "name": ""}, {"role": "text", "name": "System Configuration"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Test Bot"}, {"role": "text", "name": ""}, {"role": "link", "name": ""}, {"role": "text", "name": " Direct Leads"}, {"role": "text", "name": "2017 Cape Town - Website Enquiries"}, {"role": "text", "name": "Cape Town Campus"}, {"role": "link", "name": "Leads"}, {"role": "link", "name": "Campaign Details"}, {"role": "link", "name": "Capture Sessions"}, {"role": "link", "name": "LEAD FILTER "}, {"role": "button", "name": " NEW LEAD"}, {"role": "button", "name": "IMPORT LEADS"}, {"role": "button", "name": "EDIT"}, {"role": "button", "name": ""}, {"role": "textbox", "name": "Search"}, {"role": "button", "name": "20 "}, {"role": "text", "name": "3 items"}, {"role": "button", "name": "", "disabled": true}, {"role": "button", "name": "1", "disabled": true}, {"role": "button", "name": "", "disabled": true}, {"role": "text", "name": ""}, {"role": "text", "name": " DATE CAPTURED"}, {"role": "text", "name": "LEAD NAME"}, {"role": "text", "name": "NOTES"}, {"role": "text", "name": "STATUS"}, {"role": "text", "name": "2017-11-16"}, {"role": "text", "name": "<PERSON>"}, {"role": "text", "name": ""}, {"role": "text", "name": " I like to know when is the closing date to enrole to this course?"}, {"role": "text", "name": "Closed"}, {"role": "text", "name": "2018-01-08"}, {"role": "text", "name": "<PERSON><PERSON><PERSON>"}, {"role": "text", "name": ""}, {"role": "text", "name": " hey"}, {"role": "text", "name": "Closed"}, {"role": "text", "name": "2024-04-03"}, {"role": "text", "name": "<PERSON><PERSON><PERSON>"}, {"role": "text", "name": ""}, {"role": "text", "name": " Walk-in, spoke to mom and she asked for ID and AT info. Lead generally wants to do Fashion Design and if not running in CTN Hub, they will opt in for ID. sent info to the email"}, {"role": "text", "name": "Active"}, {"role": "button", "name": "", "disabled": true}, {"role": "button", "name": "1", "disabled": true}, {"role": "button", "name": "", "disabled": true}, {"role": "link", "name": "CANCEL"}, {"role": "button", "name": ""}]}, "metadata": {"name": "campaign-tiles", "description": "Campaign tiles container on campaigns page", "category": "campaigns", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "viewport": {"width": 1920, "height": 1080}}}