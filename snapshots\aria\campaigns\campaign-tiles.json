{"timestamp": "2025-07-14T20:41:12.834Z", "url": "https://crm.inscape.co.za/App/CrmUser/Campaign_Detail/71/", "selector": "body", "snapshot": {"role": "WebArea", "name": "Connect - CRM - Campaigns", "children": [{"role": "link", "name": " Admin"}, {"role": "link", "name": " Calendar"}, {"role": "text", "name": ""}, {"role": "link", "name": "Individual"}, {"role": "text", "name": ""}, {"role": "text", "name": "CRM"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Communication"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Enrolments"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Finances"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Academics"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "link", "name": "Facilities"}, {"role": "text", "name": ""}, {"role": "text", "name": "Classroom Management"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "link", "name": "Data Analytics"}, {"role": "text", "name": ""}, {"role": "link", "name": "Graduation"}, {"role": "text", "name": ""}, {"role": "link", "name": "Compliance"}, {"role": "text", "name": ""}, {"role": "link", "name": "Policies And Procedures"}, {"role": "text", "name": ""}, {"role": "text", "name": "System Configuration"}, {"role": "text", "name": ""}, {"role": "text", "name": ""}, {"role": "text", "name": "Test Bot"}, {"role": "text", "name": ""}, {"role": "dialog", "name": "LEAD DETAILS", "focused": true, "children": [{"role": "button", "name": "Close"}, {"role": "heading", "name": "LEAD DETAILS", "level": 4}, {"role": "button", "name": "Sales Script"}, {"role": "text", "name": "First Name"}, {"role": "textbox", "name": "First Name"}, {"role": "text", "name": "Surname"}, {"role": "textbox", "name": "Surname"}, {"role": "text", "name": "ID Number"}, {"role": "textbox", "name": "ID Number"}, {"role": "text", "name": "Current Grade"}, {"role": "combobox", "name": "Current Grade", "haspopup": "menu", "children": [{"role": "option", "name": "-Select-", "selected": true}, {"role": "option", "name": "8"}, {"role": "option", "name": "9"}, {"role": "option", "name": "10"}, {"role": "option", "name": "11"}, {"role": "option", "name": "12"}, {"role": "option", "name": "Completed"}, {"role": "option", "name": "Other"}], "value": "-Select-"}, {"role": "text", "name": "Eligible For Year"}, {"role": "spinbutton", "name": "Eligible For Year", "valuetext": "", "valuemin": 0, "valuemax": 0}, {"role": "button", "name": "SEND TO FASTBRAND"}, {"role": "heading", "name": "CONTACT INFORMATION", "level": 4}, {"role": "button", "name": " CONTACT INFORMATION VERIFIED"}, {"role": "text", "name": "Email Address"}, {"role": "textbox", "name": "Email Address"}, {"role": "text", "name": "Parent Email Address"}, {"role": "textbox", "name": "Parent Email Address"}, {"role": "text", "name": "Cellphone"}, {"role": "combobox", "name": "South Africa: +27", "haspopup": "listbox"}, {"role": "textbox", "name": "Cellphone"}, {"role": "text", "name": "Parent Cellphone"}, {"role": "combobox", "name": "South Africa: +27", "haspopup": "listbox"}, {"role": "textbox", "name": "Parent Cellphone"}, {"role": "heading", "name": "ADDITIONAL INFORMATION", "level": 4}, {"role": "text", "name": "Lead Interest"}, {"role": "combobox", "name": "Lead Interest", "haspopup": "menu", "children": [{"role": "option", "name": "-Select-", "selected": true}, {"role": "option", "name": "Architectural Design – Fundamental or Advanced"}, {"role": "option", "name": "Advanced Course in Architectural Design"}, {"role": "option", "name": "Advanced Course in Fashion Design"}, {"role": "option", "name": "Advanced Course in Graphic Design"}, {"role": "option", "name": "Advanced Course in Interior Design"}, {"role": "option", "name": "Advanced Diploma in User Experience Design"}, {"role": "option", "name": "Advanced Diploma in UX"}, {"role": "option", "name": "Architectural Technology"}, {"role": "option", "name": "BA Digital Marketing and Communication"}, {"role": "option", "name": "BDes Fashion Design"}, {"role": "option", "name": "BDes Graphic Design"}, {"role": "option", "name": "BDes Ideation Design"}, {"role": "option", "name": "BDes Interaction Design"}, {"role": "option", "name": "BDes Interior Design"}, {"role": "option", "name": "Business Management"}, {"role": "option", "name": "Design Techniques"}, {"role": "option", "name": "Entertainment"}, {"role": "option", "name": "Entrepreneur"}, {"role": "option", "name": "Fashion Design"}, {"role": "option", "name": "Fundamental Course in Architectural Design"}, {"role": "option", "name": "Fundamental Course in Digital Marketing"}, {"role": "option", "name": "Fundamental Course in Fashion Design"}, {"role": "option", "name": "Fundamental Course in Graphic Design"}, {"role": "option", "name": "Fundamental Course in Interior Design"}, {"role": "option", "name": "Fundamental Course in User Experience Design"}, {"role": "option", "name": "Graphic Design"}, {"role": "option", "name": "Higher Certificate in Architectural Technology"}, {"role": "option", "name": "Higher Certificate in Fashion Design"}, {"role": "option", "name": "Honours in Design"}, {"role": "option", "name": "Ideation Design"}, {"role": "option", "name": "Interior Decorating"}, {"role": "option", "name": "Interior Design"}, {"role": "option", "name": "Interior Design major in Commercial Design"}, {"role": "option", "name": "Interior Design major in Environmental Design"}, {"role": "option", "name": "Interior Design major in Social Design"}, {"role": "option", "name": "Make Up Workshop 2022"}, {"role": "option", "name": "Photoshop"}, {"role": "option", "name": "Photoshop Skills Training"}, {"role": "option", "name": "PR and communications"}, {"role": "option", "name": "Public Speaking"}, {"role": "option", "name": "Revit"}, {"role": "option", "name": "Sales and marketing"}], "value": "-Select-"}, {"role": "text", "name": "Current School"}, {"role": "textbox", "name": "Current School"}, {"role": "text", "name": "Next Interaction Date"}, {"role": "textbox", "name": "Next Interaction Date"}, {"role": "text", "name": "Lead Status"}, {"role": "combobox", "name": "Lead Status", "haspopup": "menu", "children": [{"role": "option", "name": "New", "selected": true}, {"role": "option", "name": "Active"}, {"role": "option", "name": "Application Created"}, {"role": "option", "name": "Provisionally Accepted"}, {"role": "option", "name": "Enrolled"}, {"role": "option", "name": "Closed"}, {"role": "option", "name": "Unresponsive"}, {"role": "option", "name": "IVA"}], "value": "New"}, {"role": "text", "name": "Referral"}, {"role": "combobox", "name": "Referral", "haspopup": "menu", "children": [{"role": "option", "name": "-Please Select-", "selected": true}, {"role": "option", "name": "Internet"}, {"role": "option", "name": "School"}, {"role": "option", "name": "Word of mouth"}, {"role": "option", "name": "Billboard"}, {"role": "option", "name": "Newspaper advert"}, {"role": "option", "name": "Magazine advert"}, {"role": "option", "name": "Social media"}], "value": "-Please Select-"}, {"role": "text", "name": "Lead Generator"}, {"role": "combobox", "name": "Lead Generator", "haspopup": "menu", "children": [{"role": "option", "name": "-Select-", "selected": true}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON>"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "<PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "<PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "Kekeletso Mokone"}, {"role": "option", "name": "Lebogang <PERSON>he"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON>"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "Mogau Maenetja"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "Noziga Zulu"}, {"role": "option", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "<PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "Sinethemba <PERSON>la"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON>sanqa Dweku"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON>"}], "value": "-Select-"}, {"role": "text", "name": "Lead Manager"}, {"role": "combobox", "name": "Lead Manager", "haspopup": "menu", "children": [{"role": "option", "name": "-Select-", "selected": true}, {"role": "option", "name": "<PERSON><PERSON>"}, {"role": "option", "name": "<PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"role": "option", "name": "Noziga Zulu"}, {"role": "option", "name": "<PERSON><PERSON>"}, {"role": "option", "name": "Sinethemba <PERSON>la"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}, {"role": "option", "name": "<PERSON><PERSON><PERSON>"}], "value": "-Select-"}, {"role": "text", "name": ""}, {"role": "text", "name": " DATE"}, {"role": "text", "name": "TYPE"}, {"role": "text", "name": "CREATED BY"}, {"role": "text", "name": "NOTE"}, {"role": "button", "name": "SAVE LEAD"}, {"role": "button", "name": "CANCEL"}]}, {"role": "link", "name": ""}, {"role": "text", "name": " Direct Leads"}, {"role": "text", "name": "2017 Cape Town - Website Enquiries"}, {"role": "text", "name": "Cape Town Campus"}, {"role": "link", "name": "Leads"}, {"role": "link", "name": "Campaign Details"}, {"role": "link", "name": "Capture Sessions"}, {"role": "link", "name": "LEAD FILTER "}, {"role": "button", "name": " NEW LEAD"}, {"role": "button", "name": "IMPORT LEADS"}, {"role": "button", "name": "EDIT"}, {"role": "button", "name": ""}, {"role": "textbox", "name": "Search"}, {"role": "button", "name": "20 "}, {"role": "text", "name": "3 items"}, {"role": "button", "name": "", "disabled": true}, {"role": "button", "name": "1", "disabled": true}, {"role": "button", "name": "", "disabled": true}, {"role": "text", "name": ""}, {"role": "text", "name": " DATE CAPTURED"}, {"role": "text", "name": "LEAD NAME"}, {"role": "text", "name": "NOTES"}, {"role": "text", "name": "STATUS"}, {"role": "text", "name": "2017-11-16"}, {"role": "text", "name": "<PERSON>"}, {"role": "text", "name": ""}, {"role": "text", "name": " I like to know when is the closing date to enrole to this course?"}, {"role": "text", "name": "Closed"}, {"role": "text", "name": "2018-01-08"}, {"role": "text", "name": "<PERSON><PERSON><PERSON>"}, {"role": "text", "name": ""}, {"role": "text", "name": " hey"}, {"role": "text", "name": "Closed"}, {"role": "text", "name": "2024-04-03"}, {"role": "text", "name": "<PERSON><PERSON><PERSON>"}, {"role": "text", "name": ""}, {"role": "text", "name": " Walk-in, spoke to mom and she asked for ID and AT info. Lead generally wants to do Fashion Design and if not running in CTN Hub, they will opt in for ID. sent info to the email"}, {"role": "text", "name": "Active"}, {"role": "button", "name": "", "disabled": true}, {"role": "button", "name": "1", "disabled": true}, {"role": "button", "name": "", "disabled": true}, {"role": "link", "name": "CANCEL"}, {"role": "button", "name": ""}]}, "metadata": {"name": "campaign-tiles", "description": "Campaign tiles container on campaigns page", "category": "campaigns", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "viewport": {"width": 1920, "height": 1080}}}