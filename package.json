{"name": "inconnect---playwright-testing", "version": "1.0.0", "description": "", "main": "index.js", "type": "commonjs", "scripts": {"test": "npx playwright test", "test:headed": "npx playwright test --headed", "test:ui": "npx playwright test --ui", "report": "npx allure generate allure-results --clean && npx allure open", "generate": "npx ts-node test-objects/student_faker.cjs", "generate-aria-snapshots": "npx ts-node scripts/generate-aria-snapshots.ts", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@eslint/js": "^8.57.0", "@faker-js/faker": "^8.4.1", "@playwright/test": "^1.51.1", "@types/node": "^20.17.6", "allure-commandline": "^2.27.0", "allure-playwright": "^3.0.6", "cross-env": "^7.0.3", "eslint": "^8.57.0", "ts-node": "^10.9.2", "typescript": "^5.6.3", "typescript-eslint": "^7.0.0"}, "dependencies": {"csv-parser": "^3.2.0", "dotenv": "^16.4.5", "form-data": "^4.0.0", "node-fetch": "^3.3.2"}}