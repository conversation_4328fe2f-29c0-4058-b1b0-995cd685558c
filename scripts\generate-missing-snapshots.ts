import { chromium } from '@playwright/test';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

/**
 * Generate missing ARIA snapshots for campaign modals
 */
async function generateMissingSnapshots() {
    console.log('🚀 Starting missing ARIA snapshot generation...');

    const browser = await chromium.launch({
        headless: false,
        slowMo: 1000
    });

    const page = await browser.newPage({
        viewport: { width: 1920, height: 1080 }
    });

    try {
        // Setup directories
        const snapshotsDir = join(process.cwd(), 'snapshots', 'aria');
        const campaignsDir = join(snapshotsDir, 'campaigns');
        
        if (!existsSync(campaignsDir)) {
            mkdirSync(campaignsDir, { recursive: true });
        }

        // Login to INCONNECT
        console.log('🔐 Logging into INCONNECT...');
        await page.goto('https://connect.inscape.co.za/UserManagement/login/');
        await page.waitForLoadState('domcontentloaded');

        // Find email field
        let emailField = await page.$('#c1');
        if (!emailField) {
            const allInputs = await page.$$('input');
            if (allInputs.length >= 2) {
                emailField = allInputs[0];
            }
        }

        // Find password field
        let passwordField = await page.$('#c2');
        if (!passwordField) {
            const allInputs = await page.$$('input');
            if (allInputs.length >= 2) {
                passwordField = allInputs[1];
            }
        }

        if (emailField && passwordField) {
            // Fill login form
            await emailField.fill('<EMAIL>');
            await passwordField.fill('Password1!');

            // Find and click submit button
            const submitSelectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                '.login-button',
                'button:has-text("Login")',
                'button:has-text("Sign In")',
                'form button',
                'button'
            ];

            let submitted = false;
            for (const selector of submitSelectors) {
                try {
                    await page.click(selector);
                    await page.waitForTimeout(2000);
                    submitted = true;
                    break;
                } catch (error) {
                    console.log(`Submit selector ${selector} failed, trying next...`);
                }
            }

            if (!submitted) {
                throw new Error('Could not find submit button');
            }

            await page.waitForLoadState('domcontentloaded');
            await page.waitForTimeout(3000);
        } else {
            throw new Error('Could not find login form fields');
        }

        console.log('✅ Authentication successful');

        // Navigate to campaigns
        console.log('🔍 Navigating to campaigns...');
        await page.click('text=CRM');
        await page.waitForTimeout(2000);
        await page.click('text=Campaigns');
        await page.waitForTimeout(5000);

        // 1. Capture Lead Upload Modal
        console.log('📸 Capturing Lead Upload modal...');
        try {
            // Select a campaign
            const campaignTiles = await page.$$('.panel.panel-default.tile.tile-hover');
            if (campaignTiles.length > 0) {
                await campaignTiles[0].click();
                await page.waitForTimeout(3000);

                const campaignRows = await page.$$('div#CampaignDataGrid table tbody tr');
                if (campaignRows.length > 0) {
                    await campaignRows[0].click();
                    await page.waitForTimeout(3000);

                    // Click Import Leads button
                    await page.click('//div[@id="crm_leads"]/div/div[2]/div[@class="row"]/div[2]/span/button[@name="c15"]');
                    await page.waitForTimeout(1000);

                    // Select lead generator and manager
                    await page.click('//form[@id="CampaignCategory_Detail"]/span[1]/div/div[@role="dialog"]//ul/li[1]');
                    await page.waitForTimeout(2000);
                    await page.click('//form[@id="CampaignCategory_Detail"]/span[2]/div/div[@role="dialog"]//ul/li[1]');
                    await page.waitForTimeout(2000);

                    const leadUploadSnapshot = await page.accessibility.snapshot();
                    if (leadUploadSnapshot) {
                        const leadUploadData = {
                            timestamp: new Date().toISOString(),
                            url: page.url(),
                            selector: 'body',
                            snapshot: leadUploadSnapshot,
                            metadata: {
                                name: 'lead-upload-modal',
                                description: 'Lead Upload modal opened by clicking Import Leads button',
                                category: 'campaigns',
                                userAgent: await page.evaluate(() => navigator.userAgent),
                                viewport: page.viewportSize() || { width: 1920, height: 1080 }
                            }
                        };
                        writeFileSync(join(campaignsDir, 'lead-upload-modal.json'), JSON.stringify(leadUploadData, null, 2));
                        console.log('✅ Lead Upload modal snapshot saved');
                    }

                    // Close modal
                    await page.keyboard.press('Escape');
                    await page.waitForTimeout(2000);
                }
            }
        } catch (error) {
            console.log('⚠️ Could not capture Lead Upload modal:', error);
        }

        // 2. Capture New Session Modal
        console.log('📸 Capturing New Session modal...');
        try {
            // Navigate back to campaigns
            await page.click('text=CRM');
            await page.waitForTimeout(2000);
            await page.click('text=Campaigns');
            await page.waitForTimeout(3000);

            // Select a campaign
            const campaignTiles = await page.$$('.panel.panel-default.tile.tile-hover');
            if (campaignTiles.length > 0) {
                await campaignTiles[0].click();
                await page.waitForTimeout(3000);

                const campaignRows = await page.$$('div#CampaignDataGrid table tbody tr');
                if (campaignRows.length > 0) {
                    await campaignRows[0].click();
                    await page.waitForTimeout(3000);

                    // Click Capture Sessions link
                    const linkCaptureSessions = page.getByRole("link", {name: "Capture Sessions"});
                    await linkCaptureSessions.click();
                    await page.waitForTimeout(1000);

                    // Click New Session button
                    const buttonNewSession = page.getByRole("button", {name: " New Session"});
                    await buttonNewSession.click();
                    await page.waitForTimeout(1000);

                    const sessionModalSnapshot = await page.accessibility.snapshot();
                    if (sessionModalSnapshot) {
                        const sessionModalData = {
                            timestamp: new Date().toISOString(),
                            url: page.url(),
                            selector: 'body',
                            snapshot: sessionModalSnapshot,
                            metadata: {
                                name: 'lead-capture-session-modal',
                                description: 'Lead Capture Session modal opened by clicking New Session button',
                                category: 'campaigns',
                                userAgent: await page.evaluate(() => navigator.userAgent),
                                viewport: page.viewportSize() || { width: 1920, height: 1080 }
                            }
                        };
                        writeFileSync(join(campaignsDir, 'lead-capture-session-modal.json'), JSON.stringify(sessionModalData, null, 2));
                        console.log('✅ Lead Capture Session modal snapshot saved');
                    }
                }
            }
        } catch (error) {
            console.log('⚠️ Could not capture New Session modal:', error);
        }

        console.log('🎉 Missing snapshots generation completed!');

    } catch (error) {
        console.error('❌ Error generating missing snapshots:', error);
        throw error;
    } finally {
        await browser.close();
    }
}

// Run the generator
generateMissingSnapshots().catch(console.error);
