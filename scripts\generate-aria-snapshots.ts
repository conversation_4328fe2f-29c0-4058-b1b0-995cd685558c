import { chromium } from '@playwright/test';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

/**
 * Simple ARIA snapshot generator script
 */
async function generateAriaSnapshots() {
    console.log('🚀 Starting ARIA snapshot generation...');

    const browser = await chromium.launch({
        headless: false,
        slowMo: 1000
    });

    const page = await browser.newPage({
        viewport: { width: 1920, height: 1080 }
    });

    try {
        // Login to INCONNECT
        console.log('🔐 Logging into INCONNECT...');
        await page.goto('https://connect.inscape.co.za/UserManagement/login/');
        await page.waitForLoadState('networkidle');

        // Debug: Check what's on the page
        console.log('🔍 Inspecting login page...');
        const title = await page.title();
        console.log(`Page title: ${title}`);

        // Try to find login form elements (updated with correct selectors)
        const emailSelectors = [
            '#c1',  // Actual username field
            'input[name="c1"]',
            'input[placeholder="Username"]',
            'input[name="email"]',
            'input[type="email"]',
            '#email',
            'input[placeholder*="email" i]',
            'input[placeholder*="Email" i]',
            'input[id*="email" i]',
            'input[name*="email" i]'
        ];

        const passwordSelectors = [
            '#c2',  // Actual password field
            'input[name="c2"]',
            'input[placeholder="Password"]',
            'input[name="password"]',
            'input[type="password"]',
            '#password',
            'input[placeholder*="password" i]',
            'input[placeholder*="Password" i]',
            'input[id*="password" i]',
            'input[name*="password" i]'
        ];

        let emailField = null;
        let passwordField = null;

        // Find email field
        for (const selector of emailSelectors) {
            try {
                emailField = await page.$(selector);
                if (emailField) {
                    console.log(`✅ Found email field with selector: ${selector}`);
                    break;
                }
            } catch (error) {
                // Continue to next selector
            }
        }

        // Find password field
        for (const selector of passwordSelectors) {
            try {
                passwordField = await page.$(selector);
                if (passwordField) {
                    console.log(`✅ Found password field with selector: ${selector}`);
                    break;
                }
            } catch (error) {
                // Continue to next selector
            }
        }

        if (!emailField || !passwordField) {
            console.log('⚠️ Could not find login form fields. Available inputs:');
            const inputs = await page.$$eval('input', inputs =>
                inputs.map(input => ({
                    type: input.type,
                    name: input.name,
                    id: input.id,
                    placeholder: input.placeholder,
                    className: input.className
                }))
            );
            console.log(JSON.stringify(inputs, null, 2));

            // Try generic approach
            const allInputs = await page.$$('input');
            if (allInputs.length >= 2) {
                emailField = allInputs[0];
                passwordField = allInputs[1];
                console.log('🔄 Using first two input fields as fallback');
            }
        }

        if (emailField && passwordField) {
            // Fill login form
            await emailField.fill('<EMAIL>');
            await passwordField.fill('Password1!');

            // Find and click submit button
            const submitSelectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                '.login-button',
                'button:has-text("Login")',
                'button:has-text("Sign In")',
                'form button',
                'button'
            ];

            let submitted = false;
            for (const selector of submitSelectors) {
                try {
                    await page.click(selector);
                    await page.waitForTimeout(2000);
                    submitted = true;
                    break;
                } catch (error) {
                    console.log(`Submit selector ${selector} failed, trying next...`);
                }
            }

            if (!submitted) {
                // Try pressing Enter on password field
                await passwordField.press('Enter');
            }

            await page.waitForLoadState('networkidle');
        } else {
            throw new Error('Could not find login form fields');
        }

        // Verify login success
        const currentUrl = page.url();
        if (currentUrl.includes('login')) {
            throw new Error('Authentication failed - still on login page');
        }
        console.log('✅ Authentication successful');

        // Ensure directories exist
        const campaignsDir = join(process.cwd(), 'snapshots', 'aria', 'campaigns');
        const baselineDir = join(process.cwd(), 'snapshots', 'aria', 'baseline');

        if (!existsSync(campaignsDir)) {
            mkdirSync(campaignsDir, { recursive: true });
        }
        if (!existsSync(baselineDir)) {
            mkdirSync(baselineDir, { recursive: true });
        }

        // Generate baseline snapshots
        console.log('📸 Generating baseline snapshots...');

        // Dashboard snapshot
        const dashboardSnapshot = await page.accessibility.snapshot();
        const dashboardData = {
            timestamp: new Date().toISOString(),
            url: page.url(),
            snapshot: dashboardSnapshot,
            metadata: {
                name: 'dashboard',
                description: 'Main dashboard after login',
                category: 'baseline',
                userAgent: await page.evaluate(() => navigator.userAgent),
                viewport: page.viewportSize() || { width: 1920, height: 1080 }
            }
        };
        writeFileSync(join(baselineDir, 'dashboard.json'), JSON.stringify(dashboardData, null, 2));
        console.log('✅ Dashboard snapshot saved');

        // Navigate to campaigns page using the correct navigation pattern
        console.log('📸 Generating campaign snapshots...');

        try {
            // First click CRM, then Campaigns (based on campaign_objects.ts)
            console.log('🔍 Navigating to CRM...');
            await page.click('text=CRM');
            await page.waitForTimeout(2000);

            console.log('🔍 Navigating to Campaigns...');
            await page.click('text=Campaigns');
            await page.waitForTimeout(5000);

            console.log('✅ Successfully navigated to campaigns page');
        } catch (error) {
            console.log('⚠️ Could not navigate to campaigns page:', error);
            console.log('📸 Capturing current page snapshot instead');
        }

        // Try to capture lead modal snapshot by following the exact flow
        console.log('📸 Attempting to capture lead modal snapshot...');
        try {
            // First try to click on a campaign tile to access campaign details
            const campaignTiles = await page.$$('.panel.panel-default.tile.tile-hover');
            if (campaignTiles.length > 0) {
                console.log(`Found ${campaignTiles.length} campaign tiles, clicking first one...`);
                await campaignTiles[0].click();
                await page.waitForTimeout(3000);

                // Now try to find and click on a campaign row to access leads
                const campaignRows = await page.$$('div#CampaignDataGrid table tbody tr');
                if (campaignRows.length > 0) {
                    console.log(`Found ${campaignRows.length} campaign rows, clicking first one...`);
                    await campaignRows[0].click();
                    await page.waitForTimeout(5000);

                    // Now click the "New Lead" button (button#c14) to open the lead modal
                    console.log('🔍 Looking for New Lead button (button#c14)...');
                    try {
                        await page.click('button#c14');
                        await page.waitForTimeout(4000);
                        console.log('✅ Clicked New Lead button, modal should be open');

                        // Try to capture the lead modal/details snapshot
                        const leadModalSelectors = [
                            '#lead-details',
                            '.lead-modal',
                            '[data-testid="lead-modal"]',
                            '.modal-dialog',
                            '#LeadModalLabel'
                        ];

                        let leadModalSnapshot;
                        let usedModalSelector = '';

                        for (const selector of leadModalSelectors) {
                            try {
                                const element = await page.$(selector);
                                if (element) {
                                    const isVisible = await element.isVisible();
                                    console.log(`🔍 Checking selector ${selector}: element found=${!!element}, visible=${isVisible}`);
                                    if (isVisible) {
                                        try {
                                            leadModalSnapshot = await page.accessibility.snapshot({ root: element });
                                            if (!leadModalSnapshot) {
                                                // Try without root element
                                                console.log(`⚠️ Root snapshot failed for ${selector}, trying full page snapshot`);
                                                leadModalSnapshot = await page.accessibility.snapshot();
                                            }
                                            usedModalSelector = selector;
                                            console.log(`✅ Found lead modal with selector: ${selector}`);
                                            break;
                                        } catch (snapshotError) {
                                            console.log(`❌ Snapshot error for ${selector}:`, snapshotError);
                                        }
                                    }
                                }
                            } catch (error) {
                                console.log(`❌ Error with selector ${selector}:`, error);
                            }
                        }

                        console.log(`🔍 leadModalSnapshot status: ${!!leadModalSnapshot}, usedModalSelector: ${usedModalSelector}`);

                        if (leadModalSnapshot) {
                            const leadModalData = {
                                timestamp: new Date().toISOString(),
                                url: page.url(),
                                selector: usedModalSelector,
                                snapshot: leadModalSnapshot,
                                metadata: {
                                    name: 'lead-modal',
                                    description: 'Lead modal dialog opened by clicking New Lead button',
                                    category: 'campaigns',
                                    userAgent: await page.evaluate(() => navigator.userAgent),
                                    viewport: page.viewportSize() || { width: 1920, height: 1080 }
                                }
                            };
                            writeFileSync(join(campaignsDir, 'lead-modal.json'), JSON.stringify(leadModalData, null, 2));
                            console.log('✅ Lead modal snapshot saved');

                            // Close the modal properly
                            console.log('🔍 Closing lead modal...');
                            try {
                                // Try clicking the close button
                                await page.click('.modal-header .close', { timeout: 5000 });
                                await page.waitForTimeout(2000);
                            } catch (error) {
                                // Fallback to pressing Escape
                                await page.keyboard.press('Escape');
                                await page.waitForTimeout(2000);
                            }
                        } else {
                            console.log('⚠️ Lead modal snapshot is null or undefined');
                        }

                    } catch (error) {
                        console.log('⚠️ Could not click New Lead button:', error);
                    }
                } else {
                    console.log('⚠️ No campaign rows found');
                }
            } else {
                console.log('⚠️ No campaign tiles found');
            }

        } catch (error) {
            console.log('⚠️ Error capturing lead modal:', error);
        }

        // Campaign tiles snapshot (based on campaign_objects.ts)
        const campaignTilesSelectors = [
            '.panel.panel-default.tile.tile-hover',  // Actual campaign tiles from campaign_objects.ts
            '.campaign-tiles-container',
            '.campaigns-container',
            '[data-testid="campaign-tiles"]',
            '.campaign-list',
            '.campaigns',
            '.panel-default'  // Fallback to panel containers
        ];

        let campaignTilesSnapshot;
        let usedSelector = '';

        for (const selector of campaignTilesSelectors) {
            try {
                const element = await page.$(selector);
                if (element) {
                    campaignTilesSnapshot = await page.accessibility.snapshot({ root: element });
                    usedSelector = selector;
                    break;
                }
            } catch (error) {
                // Continue to next selector
            }
        }

        if (!campaignTilesSnapshot) {
            // Fallback to full page snapshot
            campaignTilesSnapshot = await page.accessibility.snapshot();
            usedSelector = 'body';
        }

        const campaignTilesData = {
            timestamp: new Date().toISOString(),
            url: page.url(),
            selector: usedSelector,
            snapshot: campaignTilesSnapshot,
            metadata: {
                name: 'campaign-tiles',
                description: 'Campaign tiles container on campaigns page',
                category: 'campaigns',
                userAgent: await page.evaluate(() => navigator.userAgent),
                viewport: page.viewportSize() || { width: 1920, height: 1080 }
            }
        };
        writeFileSync(join(campaignsDir, 'campaign-tiles.json'), JSON.stringify(campaignTilesData, null, 2));
        console.log('✅ Campaign tiles snapshot saved');

        console.log('🎉 ARIA snapshots generated successfully!');
        console.log('📁 Snapshots saved to:');
        console.log(`   - ${join(baselineDir, 'dashboard.json')}`);
        console.log(`   - ${join(campaignsDir, 'campaign-tiles.json')}`);

        // Check if lead modal was also saved
        const leadModalPath = join(campaignsDir, 'lead-modal.json');
        if (existsSync(leadModalPath)) {
            console.log(`   - ${leadModalPath}`);
        }

        // Generate additional campaign modal snapshots
        await generateCampaignModals(page, campaignsDir);

    } catch (error) {
        console.error('❌ Error generating snapshots:', error);
        throw error;
    } finally {
        await browser.close();
    }
}

/**
 * Generate snapshots for campaign modals
 */
async function generateCampaignModals(page: any, campaignsDir: string) {
    console.log('📸 Generating additional campaign modal snapshots...');

    try {
        // Navigate back to campaigns page
        console.log('🔍 Navigating back to campaigns page...');
        await page.click('text=CRM');
        await page.waitForTimeout(2000);
        await page.click('text=Campaigns');
        await page.waitForTimeout(5000);

        // 1. Capture New Campaign Modal
        console.log('📸 Capturing New Campaign modal...');
        try {
            await page.click('//*[@id="c1"]'); // New Campaign button
            await page.waitForTimeout(2000);

            const campaignModalSnapshot = await page.accessibility.snapshot();
            if (campaignModalSnapshot) {
                const campaignModalData = {
                    timestamp: new Date().toISOString(),
                    url: page.url(),
                    selector: 'body',
                    snapshot: campaignModalSnapshot,
                    metadata: {
                        name: 'campaign-details-modal',
                        description: 'Campaign Details modal opened by clicking New Campaign button',
                        category: 'campaigns',
                        userAgent: await page.evaluate(() => navigator.userAgent),
                        viewport: page.viewportSize() || { width: 1920, height: 1080 }
                    }
                };
                writeFileSync(join(campaignsDir, 'campaign-details-modal.json'), JSON.stringify(campaignModalData, null, 2));
                console.log('✅ Campaign Details modal snapshot saved');
            }

            // Close modal by pressing Escape
            await page.keyboard.press('Escape');
            await page.waitForTimeout(1000);
        } catch (error) {
            console.log('⚠️ Could not capture New Campaign modal:', error);
        }

        // 2. Capture Lead Upload Modal
        console.log('📸 Capturing Lead Upload modal...');
        try {
            // First select a campaign
            const campaignTiles = await page.$$('.panel.panel-default.tile.tile-hover');
            if (campaignTiles.length > 0) {
                await campaignTiles[0].click();
                await page.waitForTimeout(3000);

                const campaignRows = await page.$$('div#CampaignDataGrid table tbody tr');
                if (campaignRows.length > 0) {
                    await campaignRows[0].click();
                    await page.waitForTimeout(3000);

                    // Click Import Leads button
                    await page.click('//div[@id="crm_leads"]/div/div[2]/div[@class="row"]/div[2]/span/button[@name="c15"]');
                    await page.waitForTimeout(1000);

                    // Select lead generator and manager
                    await page.click('//form[@id="CampaignCategory_Detail"]/span[1]/div/div[@role="dialog"]//ul/li[1]');
                    await page.waitForTimeout(2000);
                    await page.click('//form[@id="CampaignCategory_Detail"]/span[2]/div/div[@role="dialog"]//ul/li[1]');
                    await page.waitForTimeout(2000);

                    const leadUploadSnapshot = await page.accessibility.snapshot();
                    if (leadUploadSnapshot) {
                        const leadUploadData = {
                            timestamp: new Date().toISOString(),
                            url: page.url(),
                            selector: 'body',
                            snapshot: leadUploadSnapshot,
                            metadata: {
                                name: 'lead-upload-modal',
                                description: 'Lead Upload modal opened by clicking Import Leads button',
                                category: 'campaigns',
                                userAgent: await page.evaluate(() => navigator.userAgent),
                                viewport: page.viewportSize() || { width: 1920, height: 1080 }
                            }
                        };
                        writeFileSync(join(campaignsDir, 'lead-upload-modal.json'), JSON.stringify(leadUploadData, null, 2));
                        console.log('✅ Lead Upload modal snapshot saved');
                    }

                    // Close modal by pressing Escape
                    await page.keyboard.press('Escape');
                    await page.waitForTimeout(1000);
                }
            }
        } catch (error) {
            console.log('⚠️ Could not capture Lead Upload modal:', error);
        }

        // 3. Capture New Session Modal
        console.log('📸 Capturing New Session modal...');
        try {
            // Navigate back to campaigns and select one
            await page.click('text=CRM');
            await page.waitForTimeout(2000);
            await page.click('text=Campaigns');
            await page.waitForTimeout(3000);

            const campaignTiles = await page.$$('.panel.panel-default.tile.tile-hover');
            if (campaignTiles.length > 0) {
                await campaignTiles[0].click();
                await page.waitForTimeout(3000);

                const campaignRows = await page.$$('div#CampaignDataGrid table tbody tr');
                if (campaignRows.length > 0) {
                    await campaignRows[0].click();
                    await page.waitForTimeout(3000);

                    // Click Capture Sessions link
                    const linkCaptureSessions = page.getByRole("link", {name: "Capture Sessions"});
                    await linkCaptureSessions.click();
                    await page.waitForTimeout(1000);

                    // Click New Session button
                    const buttonNewSession = page.getByRole("button", {name: " New Session"});
                    await buttonNewSession.click();
                    await page.waitForTimeout(1000);

                    const sessionModalSnapshot = await page.accessibility.snapshot();
                    if (sessionModalSnapshot) {
                        const sessionModalData = {
                            timestamp: new Date().toISOString(),
                            url: page.url(),
                            selector: 'body',
                            snapshot: sessionModalSnapshot,
                            metadata: {
                                name: 'lead-capture-session-modal',
                                description: 'Lead Capture Session modal opened by clicking New Session button',
                                category: 'campaigns',
                                userAgent: await page.evaluate(() => navigator.userAgent),
                                viewport: page.viewportSize() || { width: 1920, height: 1080 }
                            }
                        };
                        writeFileSync(join(campaignsDir, 'lead-capture-session-modal.json'), JSON.stringify(sessionModalData, null, 2));
                        console.log('✅ Lead Capture Session modal snapshot saved');
                    }
                }
            }
        } catch (error) {
            console.log('⚠️ Could not capture New Session modal:', error);
        }

    } catch (error) {
        console.log('⚠️ Error generating additional campaign modals:', error);
    }
}

// Run the generator
generateAriaSnapshots().catch(console.error);