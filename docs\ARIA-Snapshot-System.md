# ARIA Snapshot System Documentation

## Overview

The ARIA Snapshot System captures real accessibility snapshots from the live INCONNECT application and uses them for automated testing. This ensures that accessibility tests are based on the actual DOM structure and ARIA attributes of your application, not hardcoded expectations.

## Architecture

### Components

1. **Snapshot Generator** (`scripts/generate-aria-snapshots.ts`)
   - Logs into the live INCONNECT application
   - Captures ARIA snapshots from key UI components
   - Saves baseline snapshots to the filesystem

2. **Snapshot Utilities** (`lib/aria-snapshot-utils.ts`)
   - Loads baseline snapshots from files
   - Compares current page state against baselines
   - Provides validation functions for modal visibility

3. **Page Object Integration** (`pages/campaigns.page.ts`)
   - Updated methods use real snapshot comparisons
   - Fallback to original hardcoded validation if snapshots fail

### Directory Structure

```
snapshots/aria/
├── README.md                 # Documentation
├── campaigns/               # Campaign-related snapshots
│   └── campaign-tiles.json  # Campaign tiles container
├── baseline/               # Core application snapshots
│   └── dashboard.json      # Main dashboard
└── generated/              # Auto-generated snapshots (temporary)
```

## Usage

### Generating Snapshots

To capture fresh ARIA snapshots from the live application:

```bash
# Generate all snapshots
npm run generate-aria-snapshots

# Or run directly
npx ts-node scripts/generate-aria-snapshots.ts
```

The generator will:
1. Launch a browser and navigate to the INCONNECT login page
2. Authenticate using the configured test credentials
3. Navigate through the application capturing snapshots
4. Save snapshots as JSON files in the appropriate directories

### Using Snapshots in Tests

The page object methods now automatically use real snapshots:

```typescript
// This now uses real baseline comparison
const isVisible = await campaignsPage.isNewLeadModalVisible();

// This compares against actual campaign tiles structure
const tilesVisible = await campaignsPage.areCampaignTilesVisible();
```

### Snapshot Comparison

The system compares current page state against baseline snapshots:

```typescript
import { compareAriaSnapshot } from '../lib/aria-snapshot-utils';

const comparison = await compareAriaSnapshot(page, 'campaign-tiles', {
    selector: '.campaign-tiles-container',
    category: 'campaigns'
});

if (comparison.matches) {
    console.log('✅ Snapshots match perfectly');
} else {
    console.log('⚠️ Differences found:', comparison.differences);
}
```

## Configuration

### Credentials

The snapshot generator uses these credentials (configured in `scripts/generate-aria-snapshots.ts`):
- **URL**: `https://connect.inscape.co.za/UserManagement/login/`
- **Username**: `<EMAIL>`
- **Password**: `Password1!`

### Snapshot Locations

Snapshots are organized by category:
- **baseline/**: Core application components (dashboard, login)
- **campaigns/**: Campaign-related UI components
- **generated/**: Temporary snapshots (auto-generated during tests)

## Maintenance

### When to Regenerate Snapshots

Regenerate snapshots when:
1. **UI Changes**: The application's accessibility structure changes
2. **New Features**: New modals or components are added
3. **Test Failures**: Snapshot comparisons start failing due to legitimate changes

### Updating Snapshots

1. **Review Changes**: Before regenerating, understand what changed in the UI
2. **Regenerate**: Run `npm run generate-aria-snapshots`
3. **Verify**: Check the generated snapshots make sense
4. **Test**: Run your test suite to ensure everything works
5. **Commit**: Add the updated snapshots to version control

### Troubleshooting

#### Authentication Issues
If the generator fails to log in:
1. Check if the login page structure changed
2. Verify credentials are still valid
3. Update selectors in the generator script if needed

#### Snapshot Comparison Failures
If tests start failing due to snapshot mismatches:
1. Check if the failures are due to legitimate UI changes
2. If yes, regenerate snapshots
3. If no, investigate what's causing the DOM differences

#### Missing Snapshots
If baseline snapshots are missing:
1. Run the generator to create them
2. Check file permissions in the snapshots directory
3. Ensure the generator completed successfully

## Best Practices

### Snapshot Management
1. **Version Control**: Always commit snapshot files to version control
2. **Review Changes**: Carefully review snapshot diffs before committing
3. **Documentation**: Document significant changes to snapshots
4. **Regular Updates**: Regenerate snapshots regularly to catch drift

### Test Design
1. **Fallback Logic**: Page objects include fallback validation methods
2. **Error Handling**: Snapshot comparison failures are handled gracefully
3. **Selective Comparison**: Use appropriate selectors to focus on relevant UI parts
4. **Tolerance**: Allow for minor differences that don't affect functionality

## Integration with Existing Tests

The ARIA snapshot system integrates seamlessly with existing tests:

1. **No Test Changes Required**: Existing test code continues to work
2. **Enhanced Validation**: Tests now use real application structure
3. **Better Error Messages**: Snapshot comparisons provide detailed diff information
4. **Fallback Support**: If snapshots fail, tests fall back to original validation

## Quick Start

1. **Generate snapshots**: `npm run generate-aria-snapshots`
2. **Run tests**: Your existing tests now use real snapshots automatically
3. **Update when needed**: Regenerate snapshots after UI changes