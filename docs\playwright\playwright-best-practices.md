# Best Practices | Playwright

## ARIA Snapshot Testing

### Overview

ARIA snapshot testing is a powerful approach for verifying the accessibility and visibility of UI elements through their ARIA attributes and roles. This method provides more reliable testing compared to traditional visibility checks as it verifies both the presence and accessibility characteristics of elements.

### Implementation

The framework provides a `captureAriaSnapshot` utility that captures accessibility information for specific elements or the entire page:

```typescript
interface AriaSnapshotConfig {
    contextSelector?: string;     // CSS selector to scope the snapshot
    interestingOnly?: boolean;    // Whether to include non-interesting elements
}

async function captureAriaSnapshot(page: Page, config: AriaSnapshotConfig = {})
```

### Lead Modal Example

Here's how ARIA snapshot testing is implemented in the Lead Modal visibility check:

```typescript
async isNewLeadModalVisible() {
    const snapshot = await captureAriaSnapshot(this.page, {
        contextSelector: '#LeadModalLabel',
        interestingOnly: true
    });

    // Verify the modal has correct ARIA attributes
    const modalNode = (snapshot?.children || []).find((node: AccessibilityNode) =>
        node.role === 'dialog' &&
        node.name === 'Lead Details'
    );

    return !!modalNode;
}
```

### Best Practices and Guidelines

#### When to Use ARIA Snapshots vs Traditional Visibility Checks

1. **Use ARIA Snapshots When:**
   - Testing modal dialogs or overlays
   - Verifying accessibility compliance
   - Checking complex UI components
   - Need to verify both visibility and accessibility properties

2. **Use Traditional Visibility Checks When:**
   - Simple element visibility verification is sufficient
   - Performance is a primary concern
   - Testing non-interactive elements

#### Structuring ARIA Snapshot Assertions

1. **Scope Your Snapshots:**
   ```typescript
   await captureAriaSnapshot(page, {
       contextSelector: '#specific-element',
       interestingOnly: true
   });
   ```

2. **Verify Multiple Properties:**
   ```typescript
   const node = snapshot.find(n => 
       n.role === 'expectedRole' &&
       n.name === 'expectedName'
   );
   ```

#### Error Handling and Logging

The framework provides built-in error handling and logging:

- Logs start of snapshot capture with context
- Records successful captures with node count
- Captures and logs errors with stack traces
- Integrates with test reporting infrastructure

#### Common ARIA Roles and Properties

For modal dialogs:
- Role: `dialog`
- Name: Descriptive title
- Properties: `aria-labelledby`, `aria-describedby`

### Integration with Test Infrastructure

1. **Logger Integration:**
   ```typescript
   const logger = Logger.getInstance();
   logger.info('Accessibility check complete', {
       nodeCount: snapshot?.children?.length
   });
   ```

2. **Test Reports:**
   - Snapshot results included in test logs
   - Failed assertions show detailed accessibility information
   - Screenshots captured on failures

### Troubleshooting Guide

Common Issues:
1. **Element Not Found:**
   - Verify contextSelector is correct
   - Check element timing/loading
   - Ensure no typos in selectors

2. **Unexpected ARIA Properties:**
   - Log full snapshot for debugging
   - Compare against rendered HTML
   - Check for dynamic attribute changes

3. **Performance Issues:**
   - Use `interestingOnly: true`
   - Limit snapshot scope with contextSelector
   - Only capture needed attributes

4. **Integration Problems:**
   - Verify logger configuration
   - Check test environment setup
   - Validate test dependencies

For detailed logs and debugging:
```typescript
logger.debug('Starting accessibility snapshot capture', {
    context: contextSelector || 'full page',
    interestingOnly
});
