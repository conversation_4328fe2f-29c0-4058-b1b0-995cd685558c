/* eslint-disable no-empty-pattern */
import { test as base } from '@playwright/test';
import { Logger } from './logger';
import type { Page } from '@playwright/test';

/**
 * Interface for ARIA snapshot configuration
 */
interface AriaSnapshotConfig {
    /**
     * Optional CSS selector to scope the snapshot to a specific element
     */
    contextSelector?: string;
    
    /**
     * Whether to include non-interesting elements in the snapshot
     * @default true
     */
    interestingOnly?: boolean;
}

// Extend the test fixture with logging capabilities
export const test = base.extend<{ logger: Logger }>({
    logger: async ({ }, use) => {
        // Create logger instance
        const logger = Logger.getInstance();
        await use(logger);
    }
});

/**
 * Captures an accessibility snapshot of the page or a specific element
 * @param page - Playwright Page object
 * @param config - Optional configuration for the snapshot
 * @returns Promise containing the accessibility snapshot
 */
export async function captureAriaSnapshot(page: Page, config: AriaSnapshotConfig = {}) {
    const logger = Logger.getInstance();
    const { contextSelector, interestingOnly = true } = config;

    try {
        logger.debug('Starting accessibility snapshot capture', {
            context: contextSelector || 'full page',
            interestingOnly
        });

        let element = undefined;
        if (contextSelector) {
            element = await page.$(contextSelector);
            if (!element) {
                throw new Error(`Context selector "${contextSelector}" not found`);
            }
        }

        const snapshot = await page.accessibility.snapshot({
            root: element || undefined,
            interestingOnly
        });

        logger.info('Successfully captured accessibility snapshot', {
            nodeCount: snapshot?.children?.length ?? 0,
            context: contextSelector || 'full page'
        });

        return snapshot;
    } catch (error) {
        const errorObj = error instanceof Error ? error : new Error(String(error));
        logger.error('Failed to capture accessibility snapshot', {
            message: errorObj.message,
            stack: errorObj.stack
        });
        throw errorObj;
    }
}

// Add automatic test logging
test.beforeEach(async ({ logger }, testInfo) => {
    try {
        logger.info(`Starting test: ${testInfo.title}`, {
            testFile: testInfo.file,
            project: testInfo.project.name
        });
    } catch (error) {
        console.error('Error in test.beforeEach:', error);
    }
});

test.afterEach(async ({ logger }, testInfo) => {
    try {
        const status = testInfo.status;
        const duration = testInfo.duration;
        
        if (status === 'passed') {
            logger.info(`Test completed: ${testInfo.title}`, {
                duration,
                status
            });
        } else if (status === 'failed' && testInfo.error) {
            // Convert TestInfoError to a format our logger can handle
            const error = {
                message: testInfo.error.message,
                value: testInfo.error.value,
                stack: testInfo.error.stack
            };
            logger.error(`Test failed: ${testInfo.title}`, error, {
                duration,
                status
            });
        } else {
            logger.warn(`Test ${status}: ${testInfo.title}`, {
                duration,
                status
            });
        }
    } catch (error) {
        console.error('Error in test.afterEach:', error);
    }
});

export { Logger, LogLevel } from './logger';
