import { chromium, <PERSON><PERSON><PERSON>, <PERSON> } from '@playwright/test';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { Logger } from './logger';

/**
 * Configuration for ARIA snapshot generation
 */
interface SnapshotConfig {
    name: string;
    description: string;
    url?: string;
    selector?: string;
    waitForSelector?: string;
    actions?: Array<{
        type: 'click' | 'fill' | 'wait' | 'hover';
        selector?: string;
        value?: string;
        timeout?: number;
    }>;
    category: 'campaigns' | 'baseline' | 'generated';
}

/**
 * Snapshot data structure
 */
interface SnapshotData {
    timestamp: string;
    url: string;
    selector?: string;
    snapshot: any;
    metadata: {
        name: string;
        description: string;
        category: string;
        userAgent: string;
        viewport: { width: number; height: number };
    };
}

/**
 * ARIA Snapshot Generator
 * Captures real ARIA snapshots from the live INCONNECT application
 */
export class AriaSnapshotGenerator {
    private browser: Browser | null = null;
    private page: Page | null = null;
    private logger: Logger;
    private baseUrl = 'https://connect.inscape.co.za';
    private credentials = {
        username: '<EMAIL>',
        password: 'Password1!'
    };

    constructor() {
        this.logger = Logger.getInstance();
    }

    /**
     * Initialize browser and authenticate
     */
    async initialize(): Promise<void> {
        this.logger.info('Initializing ARIA snapshot generator...');

        this.browser = await chromium.launch({
            headless: false, // Set to true for CI/CD
            slowMo: 1000 // Slow down for debugging
        });

        this.page = await this.browser.newPage({
            viewport: { width: 1920, height: 1080 }
        });

        await this.authenticate();
    }

    /**
     * Authenticate with the INCONNECT application
     */
    private async authenticate(): Promise<void> {
        if (!this.page) throw new Error('Page not initialized');

        this.logger.info('Authenticating with INCONNECT...');

        await this.page.goto(`${this.baseUrl}/UserManagement/login/`);
        await this.page.waitForLoadState('networkidle');

        // Fill login form
        await this.page.fill('input[name="email"], input[type="email"], #email', this.credentials.username);
        await this.page.fill('input[name="password"], input[type="password"], #password', this.credentials.password);

        // Submit login
        await this.page.click('button[type="submit"], input[type="submit"], .login-button');
        await this.page.waitForLoadState('networkidle');

        // Verify login success
        const currentUrl = this.page.url();
        if (currentUrl.includes('login')) {
            throw new Error('Authentication failed - still on login page');
        }

        this.logger.info('Authentication successful');
    }

    /**
     * Generate a single ARIA snapshot
     */
    async generateSnapshot(config: SnapshotConfig): Promise<SnapshotData> {
        if (!this.page) throw new Error('Page not initialized');

        this.logger.info(`Generating snapshot: ${config.name}`);

        // Navigate to URL if specified
        if (config.url) {
            await this.page.goto(config.url);
            await this.page.waitForLoadState('networkidle');
        }

        // Execute actions if specified
        if (config.actions) {
            for (const action of config.actions) {
                await this.executeAction(action);
            }
        }

        // Wait for specific selector if specified
        if (config.waitForSelector) {
            await this.page.waitForSelector(config.waitForSelector, { timeout: 10000 });
        }

        // Capture ARIA snapshot
        let snapshot;
        if (config.selector) {
            const element = await this.page.$(config.selector);
            if (!element) {
                throw new Error(`Element not found: ${config.selector}`);
            }
            snapshot = await this.page.accessibility.snapshot({ root: element });
        } else {
            snapshot = await this.page.accessibility.snapshot();
        }

        // Create snapshot data
        const snapshotData: SnapshotData = {
            timestamp: new Date().toISOString(),
            url: this.page.url(),
            selector: config.selector,
            snapshot,
            metadata: {
                name: config.name,
                description: config.description,
                category: config.category,
                userAgent: await this.page.evaluate(() => navigator.userAgent),
                viewport: this.page.viewportSize() || { width: 1920, height: 1080 }
            }
        };

        this.logger.info(`Snapshot captured: ${config.name}`);
        return snapshotData;
    }

    /**
     * Execute an action on the page
     */
    private async executeAction(action: NonNullable<SnapshotConfig['actions']>[0]): Promise<void> {
        if (!this.page) throw new Error('Page not initialized');

        switch (action.type) {
            case 'click':
                if (action.selector) {
                    await this.page.click(action.selector);
                    await this.page.waitForTimeout(action.timeout || 1000);
                }
                break;
            case 'fill':
                if (action.selector && action.value) {
                    await this.page.fill(action.selector, action.value);
                }
                break;
            case 'wait':
                await this.page.waitForTimeout(action.timeout || 1000);
                break;
            case 'hover':
                if (action.selector) {
                    await this.page.hover(action.selector);
                }
                break;
        }
    }

    /**
     * Save snapshot to file
     */
    saveSnapshot(snapshotData: SnapshotData, filename: string): void {
        const snapshotsDir = join(process.cwd(), 'snapshots', 'aria', snapshotData.metadata.category);

        // Ensure directory exists
        if (!existsSync(snapshotsDir)) {
            mkdirSync(snapshotsDir, { recursive: true });
        }

        const filePath = join(snapshotsDir, filename);
        writeFileSync(filePath, JSON.stringify(snapshotData, null, 2));

        this.logger.info(`Snapshot saved: ${filePath}`);
    }

    /**
     * Generate all predefined snapshots
     */
    async generateAllSnapshots(): Promise<void> {
        const configs = this.getSnapshotConfigs();

        for (const config of configs) {
            try {
                const snapshot = await this.generateSnapshot(config);
                this.saveSnapshot(snapshot, `${config.name}.json`);
            } catch (error) {
                this.logger.error(`Failed to generate snapshot ${config.name}:`, error as Error);
            }
        }
    }

    /**
     * Get predefined snapshot configurations
     */
    private getSnapshotConfigs(): SnapshotConfig[] {
        return [
            // Baseline snapshots
            {
                name: 'login-page',
                description: 'Login page accessibility structure',
                url: `${this.baseUrl}/UserManagement/login/`,
                category: 'baseline'
            },
            {
                name: 'dashboard',
                description: 'Main dashboard after login',
                category: 'baseline'
            },

            // Campaign-related snapshots
            {
                name: 'campaign-tiles',
                description: 'Campaign tiles container on campaigns page',
                actions: [
                    { type: 'click', selector: 'a[href*="campaign"], .campaigns-link, [data-testid="campaigns"]' },
                    { type: 'wait', timeout: 2000 }
                ],
                selector: '.campaign-tiles-container, .campaigns-container, [data-testid="campaign-tiles"]',
                waitForSelector: '.campaign-tiles-container, .campaigns-container',
                category: 'campaigns'
            },
            {
                name: 'lead-details-modal',
                description: 'Lead details modal dialog',
                actions: [
                    { type: 'click', selector: 'a[href*="campaign"], .campaigns-link' },
                    { type: 'wait', timeout: 2000 },
                    { type: 'click', selector: 'button#c14, .new-lead-button' },
                    { type: 'wait', timeout: 3000 }
                ],
                selector: '#LeadModalLabel, .lead-modal, [data-testid="lead-modal"]',
                waitForSelector: '#LeadModalLabel, .lead-modal',
                category: 'campaigns'
            },
            {
                name: 'campaign-details-modal',
                description: 'Campaign details modal dialog',
                actions: [
                    { type: 'click', selector: 'a[href*="campaign"], .campaigns-link' },
                    { type: 'wait', timeout: 2000 },
                    { type: 'click', selector: '//*[@id="c1"], .new-campaign-button' },
                    { type: 'wait', timeout: 3000 }
                ],
                selector: '#CampaignDetailsModalLabel, .campaign-modal',
                waitForSelector: '#CampaignDetailsModalLabel, .campaign-modal',
                category: 'campaigns'
            },
            {
                name: 'lead-capture-session-modal',
                description: 'Lead capture session modal dialog',
                actions: [
                    { type: 'click', selector: 'a[href*="campaign"], .campaigns-link' },
                    { type: 'wait', timeout: 2000 },
                    { type: 'click', selector: 'a[href*="capture"], .capture-sessions-link' },
                    { type: 'wait', timeout: 2000 },
                    { type: 'click', selector: 'button[name*="session"], .new-session-button' },
                    { type: 'wait', timeout: 3000 }
                ],
                selector: '#LeadCaptureSessionModalLabel, .session-modal',
                waitForSelector: '#LeadCaptureSessionModalLabel, .session-modal',
                category: 'campaigns'
            }
        ];
    }

    /**
     * Clean up resources
     */
    async cleanup(): Promise<void> {
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
            this.page = null;
        }
        this.logger.info('ARIA snapshot generator cleaned up');
    }
}

/**
 * CLI script to generate ARIA snapshots
 */
async function main() {
    const generator = new AriaSnapshotGenerator();

    try {
        await generator.initialize();
        await generator.generateAllSnapshots();
        console.log('✅ All ARIA snapshots generated successfully!');
    } catch (error) {
        console.error('❌ Failed to generate ARIA snapshots:', error);
        process.exit(1);
    } finally {
        await generator.cleanup();
    }
}

// Run if called directly
if (require.main === module) {
    main();
}