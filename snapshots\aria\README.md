# ARIA Snapshots

This directory contains baseline ARIA snapshots captured from the live INCONNECT application. These snapshots are used for accessibility testing and UI verification.

## Directory Structure

```
snapshots/aria/
├── README.md                 # This file
├── campaigns/               # Campaign-related snapshots
│   ├── campaign-details-modal.json
│   ├── campaign-tiles.j<PERSON>
│   ├── lead-details-modal.json
│   └── lead-capture-session-modal.json
├── baseline/               # Core application snapshots
│   ├── login-page.json
│   └── dashboard.json
└── generated/              # Auto-generated snapshots (temporary)
```

## Snapshot Files

Each snapshot file contains:
- **timestamp**: When the snapshot was captured
- **url**: The page URL where the snapshot was taken
- **selector**: The CSS selector used to scope the snapshot
- **snapshot**: The actual ARIA accessibility tree
- **metadata**: Additional context about the capture

## Usage

Snapshots are automatically loaded and compared during test execution using the `compareAriaSnapshot` utility function.

## Regenerating Snapshots

To update snapshots when the UI changes:

```bash
# Generate all snapshots
npm run generate-aria-snapshots

# Generate specific page snapshots
npm run generate-aria-snapshots -- --page campaigns

# Generate with debug output
npm run generate-aria-snapshots -- --debug
```

## Best Practices

1. **Regenerate snapshots** after UI changes that affect accessibility structure
2. **Review snapshot diffs** carefully before committing changes
3. **Keep snapshots focused** - capture only the relevant UI components
4. **Document changes** when updating baseline snapshots