import { Page } from '@playwright/test';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import { Logger } from './logger';
import { captureAriaSnapshot } from './test-logger';

/**
 * Snapshot data structure (matches generator)
 */
interface SnapshotData {
    timestamp: string;
    url: string;
    selector?: string;
    snapshot: any;
    metadata: {
        name: string;
        description: string;
        category: string;
        userAgent: string;
        viewport: { width: number; height: number };
    };
}

/**
 * Comparison result
 */
interface ComparisonResult {
    matches: boolean;
    differences: string[];
    currentSnapshot: any;
    baselineSnapshot: any;
    metadata: {
        baselineTimestamp: string;
        comparisonTimestamp: string;
        snapshotName: string;
    };
}

/**
 * ARIA Snapshot Utilities
 * Helper functions for loading and comparing ARIA snapshots
 */
export class AriaSnapshotUtils {
    private static logger = Logger.getInstance();

    /**
     * Load a baseline snapshot from file
     */
    static loadBaselineSnapshot(name: string, category: string = 'campaigns'): SnapshotData | null {
        const snapshotPath = join(process.cwd(), 'snapshots', 'aria', category, `${name}.json`);

        if (!existsSync(snapshotPath)) {
            this.logger.warn(`Baseline snapshot not found: ${snapshotPath}`);
            return null;
        }

        try {
            const content = readFileSync(snapshotPath, 'utf-8');
            return JSON.parse(content) as SnapshotData;
        } catch (error) {
            this.logger.error(`Failed to load baseline snapshot ${name}:`, error);
            return null;
        }
    }

    /**
     * Compare current page state against baseline snapshot
     */
    static async compareAriaSnapshot(
        page: Page,
        snapshotName: string,
        options: {
            selector?: string;
            category?: string;
            interestingOnly?: boolean;
        } = {}
    ): Promise<ComparisonResult> {
        const { selector, category = 'campaigns', interestingOnly = true } = options;

        // Load baseline snapshot
        const baseline = this.loadBaselineSnapshot(snapshotName, category);
        if (!baseline) {
            throw new Error(`Baseline snapshot not found: ${snapshotName}`);
        }

        // Capture current snapshot
        const currentSnapshot = await captureAriaSnapshot(page, {
            contextSelector: selector || baseline.selector,
            interestingOnly
        });

        // Compare snapshots
        const comparison = this.compareSnapshots(baseline.snapshot, currentSnapshot);

        return {
            matches: comparison.matches,
            differences: comparison.differences,
            currentSnapshot,
            baselineSnapshot: baseline.snapshot,
            metadata: {
                baselineTimestamp: baseline.timestamp,
                comparisonTimestamp: new Date().toISOString(),
                snapshotName
            }
        };
    }

    /**
     * Compare two ARIA snapshots
     */
    private static compareSnapshots(baseline: any, current: any): { matches: boolean; differences: string[] } {
        const differences: string[] = [];

        if (!baseline && !current) {
            return { matches: true, differences: [] };
        }

        if (!baseline || !current) {
            differences.push(`One snapshot is null: baseline=${!!baseline}, current=${!!current}`);
            return { matches: false, differences };
        }

        this.compareNodes(baseline, current, '', differences);

        return {
            matches: differences.length === 0,
            differences
        };
    }

    /**
     * Recursively compare ARIA nodes
     */
    private static compareNodes(baseline: any, current: any, path: string, differences: string[]): void {
        // Compare basic properties
        const properties = ['role', 'name', 'description', 'value'];

        for (const prop of properties) {
            if (baseline[prop] !== current[prop]) {
                differences.push(`${path}.${prop}: expected "${baseline[prop]}", got "${current[prop]}"`);
            }
        }

        // Compare children
        const baselineChildren = baseline.children || [];
        const currentChildren = current.children || [];

        if (baselineChildren.length !== currentChildren.length) {
            differences.push(`${path}.children: expected ${baselineChildren.length} children, got ${currentChildren.length}`);
        }

        // Compare each child
        const maxLength = Math.max(baselineChildren.length, currentChildren.length);
        for (let i = 0; i < maxLength; i++) {
            const baselineChild = baselineChildren[i];
            const currentChild = currentChildren[i];

            if (baselineChild && currentChild) {
                this.compareNodes(baselineChild, currentChild, `${path}.children[${i}]`, differences);
            } else if (baselineChild) {
                differences.push(`${path}.children[${i}]: missing in current snapshot`);
            } else if (currentChild) {
                differences.push(`${path}.children[${i}]: unexpected in current snapshot`);
            }
        }
    }

    /**
     * Validate that a modal is visible with expected ARIA attributes
     */
    static async validateModalVisibility(
        page: Page,
        snapshotName: string,
        expectedRole: string = 'dialog',
        expectedName?: string
    ): Promise<boolean> {
        try {
            const comparison = await this.compareAriaSnapshot(page, snapshotName);

            if (!comparison.matches) {
                this.logger.warn(`Modal snapshot comparison failed for ${snapshotName}:`, comparison.differences);
            }

            // Additional validation for modal-specific attributes
            const snapshot = comparison.currentSnapshot;
            if (snapshot) {
                const hasCorrectRole = this.findNodeWithRole(snapshot, expectedRole);
                const hasCorrectName = expectedName ? this.findNodeWithName(snapshot, expectedName) : true;

                return hasCorrectRole && hasCorrectName;
            }

            return false;
        } catch (error) {
            this.logger.error(`Failed to validate modal visibility for ${snapshotName}:`, error);
            return false;
        }
    }

    /**
     * Find a node with specific role in the snapshot tree
     */
    private static findNodeWithRole(node: any, role: string): boolean {
        if (node.role === role) {
            return true;
        }

        if (node.children) {
            for (const child of node.children) {
                if (this.findNodeWithRole(child, role)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Find a node with specific name in the snapshot tree
     */
    private static findNodeWithName(node: any, name: string): boolean {
        if (node.name === name) {
            return true;
        }

        if (node.children) {
            for (const child of node.children) {
                if (this.findNodeWithName(child, name)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Get all available baseline snapshots
     */
    static getAvailableSnapshots(category: string = 'campaigns'): string[] {
        const snapshotsDir = join(process.cwd(), 'snapshots', 'aria', category);

        if (!existsSync(snapshotsDir)) {
            return [];
        }

        try {
            const fs = require('fs');
            return fs.readdirSync(snapshotsDir)
                .filter((file: string) => file.endsWith('.json'))
                .map((file: string) => file.replace('.json', ''));
        } catch (error) {
            this.logger.error(`Failed to read snapshots directory:`, error);
            return [];
        }
    }
}

/**
 * Convenience function for comparing ARIA snapshots
 */
export async function compareAriaSnapshot(
    page: Page,
    snapshotName: string,
    options?: {
        selector?: string;
        category?: string;
        interestingOnly?: boolean;
    }
): Promise<ComparisonResult> {
    return AriaSnapshotUtils.compareAriaSnapshot(page, snapshotName, options);
}

/**
 * Convenience function for validating modal visibility
 */
export async function validateModalVisibility(
    page: Page,
    snapshotName: string,
    expectedRole?: string,
    expectedName?: string
): Promise<boolean> {
    return AriaSnapshotUtils.validateModalVisibility(page, snapshotName, expectedRole, expectedName);
}